const express = require('express');
const fs = require('fs');
const path = require('path');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware to parse JSON
app.use(express.json());

// Load data from JSON file
let data = [];
try {
  const dataPath = path.join(__dirname, 'data.json');
  const rawData = fs.readFileSync(dataPath, 'utf8');
  data = JSON.parse(rawData);
  console.log(`Loaded ${data.length} records from data.json`);
} catch (error) {
  console.error('Error loading data:', error.message);
  process.exit(1);
}

// Home route - returns all 1000 data items
app.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Welcome to the Node.js Express API',
      totalRecords: data.length,
      data: data
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving data',
      error: error.message
    });
  }
});

// Additional route to get data with pagination (optional)
app.get('/api/data', (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    const paginatedData = data.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      page: page,
      limit: limit,
      totalRecords: data.length,
      totalPages: Math.ceil(data.length / limit),
      data: paginatedData
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving paginated data',
      error: error.message
    });
  }
});

// Route to get a single record by ID
app.get('/api/data/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const record = data.find(item => item.id === id);
    
    if (!record) {
      return res.status(404).json({
        success: false,
        message: 'Record not found'
      });
    }
    
    res.json({
      success: true,
      data: record
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving record',
      error: error.message
    });
  }
});

// Health check route
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server is running on http://localhost:${PORT}`);
  console.log(`📊 Serving ${data.length} records`);
  console.log(`🏠 Home endpoint: http://localhost:${PORT}/`);
  console.log(`📄 Paginated data: http://localhost:${PORT}/api/data`);
  console.log(`❤️  Health check: http://localhost:${PORT}/health`);
});

module.exports = app;

const fs = require('fs');

// Sample data arrays for generating random data
const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>uyen', 'Hill', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Rivera', 'Campbell', '<PERSON>', '<PERSON>', '<PERSON>'
];

const cities = [
  'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego',
  'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco',
  'Indianapolis', 'Seattle', 'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville', 'Detroit',
  'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville', 'Baltimore', 'Milwaukee', 'Albuquerque',
  'Tucson', 'Fresno', 'Sacramento', 'Kansas City', 'Long Beach', 'Mesa', 'Atlanta', 'Colorado Springs'
];

const occupations = [
  'Software Engineer', 'Product Manager', 'Data Analyst', 'UX Designer', 'DevOps Engineer', 'Marketing Manager',
  'Sales Representative', 'Accountant', 'HR Manager', 'Business Analyst', 'Project Manager', 'Graphic Designer',
  'Customer Service Rep', 'Operations Manager', 'Financial Analyst', 'Content Writer', 'Social Media Manager',
  'Quality Assurance', 'System Administrator', 'Database Administrator', 'Web Developer', 'Mobile Developer',
  'Security Analyst', 'Network Engineer', 'Technical Writer', 'Consultant', 'Researcher', 'Teacher',
  'Nurse', 'Doctor', 'Lawyer', 'Architect', 'Engineer', 'Scientist', 'Pharmacist', 'Therapist'
];

const departments = [
  'Engineering', 'Product', 'Marketing', 'Sales', 'Human Resources', 'Finance', 'Operations', 'Design',
  'Analytics', 'Customer Support', 'Legal', 'Research', 'Quality Assurance', 'IT', 'Security'
];

// Function to get random item from array
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Function to generate random salary between 40k and 150k
function getRandomSalary() {
  return Math.floor(Math.random() * (150000 - 40000) + 40000);
}

// Function to generate random age between 22 and 65
function getRandomAge() {
  return Math.floor(Math.random() * (65 - 22) + 22);
}

// Generate 1000 records
const data = [];
for (let i = 1; i <= 1000; i++) {
  const firstName = getRandomItem(firstNames);
  const lastName = getRandomItem(lastNames);
  const name = `${firstName} ${lastName}`;
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
  
  data.push({
    id: i,
    name: name,
    email: email,
    age: getRandomAge(),
    city: getRandomItem(cities),
    occupation: getRandomItem(occupations),
    salary: getRandomSalary(),
    department: getRandomItem(departments)
  });
}

// Write to JSON file
fs.writeFileSync('data.json', JSON.stringify(data, null, 2));
console.log('Generated 1000 records and saved to data.json');
